interface Product {
  name: string;
  category: string;
  count: number;
  change: string;
  changeType: "positive" | "negative" | "neutral";
}

interface TopProductsProps {
  products: Product[];
}

export default function TopProducts({ products }: TopProductsProps) {
  const getChangeColorClass = (changeType: string) => {
    switch (changeType) {
      case "positive":
        return "text-green-600";
      case "negative":
        return "text-red-600";
      case "neutral":
        return "text-yellow-600";
      default:
        return "text-gray-600";
    }
  };

  const getCurrentMonthRange = () => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const today = new Date();
    
    const formatDate = (date: Date) => {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${months[date.getMonth()]} ${date.getDate()}`;
    };
    
    return `${formatDate(firstDay)} - ${formatDate(today)}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900">Top Products by Spend</h3>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="text-xs bg-gray-100 text-gray-800 font-medium py-1 px-2 rounded border-0 pr-6">
                This Month
              </div>
            </div>
            <div className="relative">
              <button className="text-xs bg-white border border-gray-300 hover:bg-gray-50 text-gray-600 font-medium py-1 px-2 rounded flex items-center space-x-1">
                <i className="fa-solid fa-calendar-alt"></i>
                <span>{getCurrentMonthRange()}</span>
              </button>
            </div>
          </div>
        </div>
        <div className="space-y-3">
          {products.map((product, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center">
                <div>
                  <div className="font-medium text-gray-900">
                    {product.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {product.category}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-gray-900">
                  {product.count}
                </div>
                {/* <div
                  className={`text-sm ${getChangeColorClass(product.changeType)}`}
                >
                  {product.change}
                </div> */}
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* <div className="bg-gray-50 px-5 py-3 border-t border-gray-200">
        <span className="text-sm font-medium text-primary-600 hover:text-primary-700 cursor-pointer">
          View All Products
        </span>
      </div> */}
    </div>
  );
}
