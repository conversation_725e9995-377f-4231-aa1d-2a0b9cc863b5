import { useEffect } from 'react';

interface KPICardProps {
  title: string;
  value: string | number;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: string;
  description: string;
  chartId: string;
  chartType?: 'areaspline' | 'column' | 'line';
  chartData?: number[];
  chartColor?: string;
}

declare global {
  interface Window {
    Highcharts: any;
  }
}

export default function KPICard({
  title,
  value,
  change,
  changeType,
  icon,
  description,
  chartId,
  chartType = 'areaspline',
  chartData = [],
  chartColor = '#0ea5e9'
}: KPICardProps) {
  useEffect(() => {
    const initChart = () => {
      if (typeof window !== 'undefined' && window.Highcharts && chartData.length > 0) {
        window.Highcharts.chart(chartId, {
          chart: {
            type: chartType,
            height: 64,
            margin: [0, 0, 0, 0],
            backgroundColor: 'transparent'
          },
          title: { text: null },
          credits: { enabled: false },
          legend: { enabled: false },
          xAxis: {
            labels: { enabled: false },
            title: { text: null },
            startOnTick: false,
            endOnTick: false,
            tickPositions: [],
            lineWidth: 0
          },
          yAxis: {
            labels: { enabled: false },
            title: { text: null },
            startOnTick: false,
            endOnTick: false,
            tickPositions: [],
            lineWidth: 0
          },
          tooltip: { enabled: false },
          plotOptions: {
            areaspline: {
              lineWidth: 1.5,
              marker: { enabled: false },
              fillOpacity: 0.2
            },
            column: {
              borderWidth: 0,
              pointPadding: chartType === 'column' ? 0.1 : 0.05,
              groupPadding: chartType === 'column' ? 0.1 : 0.05
            },
            line: {
              lineWidth: 2,
              marker: { enabled: false },
              states: {
                hover: { lineWidth: 2 }
              }
            }
          },
          series: [{
            name: title,
            data: chartData,
            color: chartColor
          }]
        });
      }
    };

    const timer = setTimeout(initChart, 100);
    return () => clearTimeout(timer);
  }, [chartId, chartType, chartData, chartColor, title]);

  const getChangeColorClass = (type: string) => {
    switch (type) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'neutral': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-5">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <i className={`fa-solid ${icon} text-primary-500`}></i>
        </div>
        <div className="flex items-baseline">
          <span className="text-2xl font-semibold text-gray-900">{value}</span>
          {/* <span className={`ml-2 text-sm font-medium ${getChangeColorClass(changeType)}`}>
            {change}
          </span> */}
        </div>
        <div className="mt-1 text-xs text-gray-500">{description}</div>
        <div id={chartId} className="h-16 mt-3"></div>
      </div>
      {/* <div className="bg-gray-50 px-5 py-2 border-t border-gray-200">
        <span className="text-xs font-medium text-primary-600 hover:text-primary-700 cursor-pointer">
          View Full Report
        </span>
      </div> */}
    </div>
  );
}