import React, { useState, useCallback } from "react";
import {
  Button,
  Modal,
  TextField,
  BlockStack,
  InlineStack,
  Text,
  Card,
  Icon,
  Badge
} from "@shopify/polaris";
import { SearchIcon, LocationIcon } from "@shopify/polaris-icons";
import { RoleAssignmentItem } from "app/types/shopify-data";

interface Props {
  locations: RoleAssignmentItem[];
  selectedLocation?: RoleAssignmentItem | null;
  onLocationSelect: (location: RoleAssignmentItem) => void;
  placeholder?: string;
  label?: string;
}

export const CustomerLocationSelector: React.FC<Props> = ({
  locations,
  selectedLocation,
  onLocationSelect,
  placeholder = "Select a location",
  label = "Location"
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const handleModalClose = useCallback(() => {
    setIsModalOpen(false);
    setSearchTerm("");
  }, []);

  const handleLocationSelect = useCallback((location: RoleAssignmentItem) => {
    onLocationSelect(location);
    handleModalClose();
  }, [onLocationSelect, handleModalClose]);

  const filteredLocations = locations.filter(location =>
    location.companyLocation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    location.companyLocation.externalId?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const modalMarkup = (
    <Modal
      open={isModalOpen}
      onClose={handleModalClose}
      title={`Select ${label}`}
      primaryAction={{
        content: "Cancel",
        onAction: handleModalClose,
      }}
      secondaryActions={[]}
    >
      <Modal.Section>
        <BlockStack gap="400">
          <TextField
            label=""
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder={`Search ${label.toLowerCase()}s...`}
            prefix={<Icon source={SearchIcon} />}
            autoComplete="off"
          />

          <div style={{ maxHeight: "400px", overflowY: "auto" }}>
            <BlockStack gap="200">
              {filteredLocations.length === 0 ? (
                <Card>
                  <BlockStack gap="200" align="center">
                    <Icon source={LocationIcon} tone="subdued" />
                    <Text as="p" tone="subdued">
                      {searchTerm ? "No locations found matching your search." : "No locations available."}
                    </Text>
                  </BlockStack>
                </Card>
              ) : (
                filteredLocations.map((location) => (
                  <div
                    key={location.id}
                    onClick={() => handleLocationSelect(location)}
                    style={{ cursor: "pointer" }}
                  >
                    <Card padding="300">
                      <BlockStack gap="200">
                        <InlineStack align="space-between">
                          <Text as="h3" variant="bodyMd" fontWeight="semibold">
                            {location.companyLocation?.name}
                          </Text>
                        </InlineStack>
                        {location?.companyLocation?.externalId && (
                          <Text as="p" variant="bodySm" tone="subdued">
                            {location.companyLocation.externalId}
                          </Text>
                        )}
                      </BlockStack>
                    </Card>
                  </div>
                ))
              )}
            </BlockStack>
          </div>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <BlockStack gap="200">
        <Text as="h3" variant="bodyMd" fontWeight="semibold">
          Select Location
        </Text>
        <Button
          onClick={() => setIsModalOpen(true)}
          variant="tertiary"
          textAlign="left"
          fullWidth
          icon={LocationIcon}
        >
          {selectedLocation ? selectedLocation?.companyLocation?.name : placeholder}
        </Button>
        {modalMarkup}
      </BlockStack>
    </div>
  );
};
