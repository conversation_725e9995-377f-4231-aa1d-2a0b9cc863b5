import WelcomeSection from "./dashboard/WelcomeSection";
import KPICard from "./dashboard/KPICard";
import TopProducts from "./dashboard/TopProducts";
import UsageBreakdown from "./dashboard/UsageBreakdown";
import RecentOrders from "./dashboard/RecentOrders";
import type { ICustomer } from "app/types/shopify-data";
import { useLocation } from "../contexts/LocationContext";
import { useClientStats } from "../hooks/useCustomerActions";
import { useEffect } from "react";

interface Location {
  id: string;
  name: string;
  externalId: string;
}

export default function Dashboard({ customer }: { customer?: ICustomer }) {
  const { selectedLocation, setSelectedLocation, locationId } = useLocation();
  const { data: clientStats, isLoading: statsLoading } = useClientStats(
    customer, 
    selectedLocation ? [selectedLocation.externalId] : []
  );

  const listLocations = customer?.companyContactProfiles?.map(profile => {
    return {
      companyName: profile.company.name,
      companyId: profile.company.id,
      locations: profile.company.locations.nodes.map(loc => ({
        id: loc.id,
        name: loc.name,
        externalId: loc.externalId
      })),
      roles: profile.roleAssignments.nodes.map(role => ({
        id: role.companyLocation.id,
        name: role.companyLocation.name,
        externalId: role.companyLocation.externalId || '',
        roleName: role.role.name
      }))
    };
  });

  const listCompanies = customer?.companyContactProfiles?.map(profile => {
    return {
      companyName: profile.company.name.split('|')[0]?.trim(),
      externalId: profile.company.externalId,
      companyId: profile.company.id,
    };
  });
  
  const clientCode = listCompanies?.[0]?.externalId;
  const clientName = listCompanies?.[0]?.companyName;

  const availableLocations = listLocations?.[0]?.roles || [];

  useEffect(() => {
    if (!selectedLocation && locationId && availableLocations.length > 0) {
      const location = availableLocations.find(loc => loc.id.includes(locationId));
      if (location) {
        setSelectedLocation(location);
      }
    }
  }, [locationId, availableLocations, selectedLocation]);

  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
  };

  const kpiData = {
    totalSpend: {
      value: clientStats ? `£${clientStats.totalSpent?.toLocaleString() || '0'}` : '£0',
      change: '+0%',
      changeType: 'neutral' as const,
    },
    topProducts: {
      value: clientStats?.topProducts?.length || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
    activeSites: {
      value: clientStats?.activeSites || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
    ordersThisMonth: {
      value: clientStats?.activeOrders || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
  };

  const topProductsData = clientStats?.topProducts?.map((product, index) => ({
    name: product.productName,
    category: 'Product Category', 
    count: product.count,
    change: '+0%',
    changeType: 'neutral' as const,
  })) || [];

  const staticData = {
    user: {
      name: "John Doe",
      initials: "JD",
      role: "Account Manager",
      lastLogin: "17 Jul 2025, 13:45",
    },
    recentOrders: [
      {
        id: "#ORD-2567",
        date: "Jul 15, 2025",
        site: "Manchester HQ",
        items: 12,
        total: "£1,245.00",
        status: "delivered" as const,
      },
      {
        id: "#ORD-2566",
        date: "Jul 12, 2025",
        site: "Birmingham Office",
        items: 8,
        total: "£876.50",
        status: "delivered" as const,
      },
      {
        id: "#ORD-2565",
        date: "Jul 10, 2025",
        site: "London Branch",
        items: 15,
        total: "£1,890.25",
        status: "shipped" as const,
      },
      {
        id: "#ORD-2564",
        date: "Jul 8, 2025",
        site: "Glasgow Store",
        items: 6,
        total: "£543.75",
        status: "processing" as const,
      },
    ],
  };

  if (!selectedLocation && availableLocations.length > 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Location</h2>
          <p className="text-gray-600 mb-6">Please select a location to view the dashboard:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableLocations.map((location) => (
              <button
                key={location.id}
                onClick={() => handleLocationSelect(location)}
                className="p-4 cursor-pointer border border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors text-left"
              >
                <h3 className="font-semibold text-gray-900">{location.name}</h3>
                <p className="text-sm text-gray-500">ID: {location.externalId}</p>
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (availableLocations.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">No Locations Available</h2>
          <p className="text-gray-600">No locations are available for this customer.</p>
        </div>
      </div>
    );
  }

  if (selectedLocation && statsLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedLocation) {
    return null;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">Selected Location:</span>
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              {selectedLocation.name}
            </span>
            <button
              onClick={() => setSelectedLocation(null)}
              className="text-sm cursor-pointer text-gray-500 hover:text-gray-700 underline"
            >
              Change Location
            </button>
          </div>
        </div>

        <WelcomeSection
          userName={[customer?.firstName, customer?.lastName].filter(Boolean).join(' ')}
          userRole={customer?.email || ''}
          clientCode={clientCode}
          clientName={clientName}
        />

        <div className="flex">
          <div className="flex-1 pr-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <KPICard
                title="Total Spend"
                value={kpiData.totalSpend.value}
                change={kpiData.totalSpend.change}
                changeType={kpiData.totalSpend.changeType}
                icon="fa-pound-sign"
                description="Total spend from API data"
                chartId="total-spend-chart"
                chartType="areaspline"
                chartData={[5000, 7500, 6800, 11200, 13500, 14800, 16500, 18200, 21000, clientStats?.totalSpent || 0]}
                chartColor="#0ea5e9"
              />

              <KPICard
                title="Top Products"
                value={kpiData.topProducts.value}
                change={kpiData.topProducts.change}
                changeType={kpiData.topProducts.changeType}
                icon="fa-box"
                description="Unique products from API data"
                chartId="top-products-chart"
                chartType="column"
                chartData={[32, 35, 38, 42, 45, 41, 44, 46, clientStats?.topProducts?.length || 0]}
                chartColor="#0284c7"
              />

              <KPICard
                title="Active Sites"
                value={kpiData.activeSites.value}
                change={kpiData.activeSites.change}
                changeType={kpiData.activeSites.changeType}
                icon="fa-map-marker-alt"
                description="Active sites from API data"
                chartId="active-sites-chart"
                chartType="line"
                chartData={[10, 10, 11, 12, 12, 12, 12, 12, 12, clientStats?.activeSites || 0]}
                chartColor="#0369a1"
              />

              <KPICard
                title="Orders This Month"
                value={kpiData.ordersThisMonth.value}
                change={kpiData.ordersThisMonth.change}
                changeType={kpiData.ordersThisMonth.changeType}
                icon="fa-shopping-cart"
                description="Orders from API data"
                chartId="orders-chart"
                chartType="column"
                chartData={[42, 40, 38, 45, 43, 39, 41, 38, 39, clientStats?.activeOrders || 0]}
                chartColor="#075985"
              />
            </div>

            <TopProducts products={topProductsData.length > 0 ? topProductsData : [
              {
                name: "No products data available",
                category: "N/A",
                count: 0,
                change: "+0%",
                changeType: "neutral" as const,
              }
            ]} />
{/* 
            <UsageBreakdown />

            <RecentOrders orders={staticData.recentOrders} /> */}
          </div>
        </div>
    </div>
  );
}