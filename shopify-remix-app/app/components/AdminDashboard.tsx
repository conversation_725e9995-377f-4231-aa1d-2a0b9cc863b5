import React, { useEffect, useMemo, useState } from 'react';
import { AdminHeader } from './AdminHeader';
import { AdminNavigationTabs } from './AdminNavigationTabs';
import { CustomerSidebar } from './CustomerSidebar';
import { CustomerOverview } from './CustomerOverview';
import { CustomerKPICards } from './CustomerKPICards';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { ICustomer, RoleAssignmentItem } from 'app/types/shopify-data';
import { useClientStats } from 'app/hooks/useCustomerActions';
import { CustomerLocationSelector } from './CustomerLocationSelector';

export interface Customer {
  name: string;
  manager: string;
  value: string;
  status: string;
  id: string;
  memberSince: string;
  lastLogin: string;
  totalSpend: string;
  activeSites: number;
  ordersYTD: number;
  accountHealth: string;
}

export interface Activity {
  id: string;
  type: string;
  message: string;
  time: string;
}

export interface QuickAction {
  id: string;
  text: string;
}

interface Props {
  customers: ICustomer[];
  searchTerm: string;
  setSearchTerm: (searchTerm: string) => void;
  isLoadingCustomers: boolean;
  onCustomerInspect: (customerId: string) => void;
}

const AdminDashboard: React.FC<Props> = (props: Props) => {
  const { customers, searchTerm, setSearchTerm, isLoadingCustomers, onCustomerInspect } = props;
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<RoleAssignmentItem | null>(null);
  const [aiQuery, setAiQuery] = useState('');
  const { data: clientStats, isLoading: isLoadingClientStats } = useClientStats(
    customers?.find(c => c.id === selectedCustomer),
    selectedLocation ? [selectedLocation?.companyLocation?.externalId || ''] : []
  );

  console.log("clientStats", clientStats);
  const displayCustomers = useMemo(() => {
    const filteredCustomers = searchTerm && !isLoadingCustomers ? customers : customers.filter(c => c.companyContactProfiles?.length > 0)
    return filteredCustomers.map(c => ({
      name: c.companyContactProfiles?.[0]?.company?.name || '',
      manager: [c.firstName, c.lastName].join(' ') + ' - ' + c.companyContactProfiles?.[0]?.roleAssignments?.nodes?.[0]?.role?.name || '',
      value: `£${Number(c.amountSpent?.amount || 0).toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      status: c.state,
      id: c.id,
      memberSince: c.createdAt,
      lastLogin: '',
      totalSpend: c.amountSpent?.amount,
      activeSites: c.companyContactProfiles?.[0]?.company?.locations?.nodes?.length || 0,
      ordersYTD: Number(c.numberOfOrders || '0'),
      accountHealth: 'Excellent'
    }))
  }, [customers, searchTerm, isLoadingCustomers])

  const quickActions: QuickAction[] = [
    { id: '1', text: "What did ACME spend last month?" },
    { id: '2', text: "Top 5 products by quantity" },
    { id: '3', text: "Usage breakdown by site" },
    { id: '4', text: "Payment history overview" }
  ];

  const selectedCustomerData = displayCustomers.find(c => c.id === selectedCustomer);
  const selectedCustomerFullData = customers?.find(c => c.id === selectedCustomer);

  const recentActivities = useMemo(() => {
    return (customers?.find(c => c.id === selectedCustomer)?.events?.nodes?.map(e => ({
      id: e.id,
      type: e.action,
      message: e.message,
      time: e.createdAt,
    })) || []).reverse()
  }, [customers, selectedCustomer]) as Activity[]

  useEffect(() => {
    if (selectedCustomer) {
      const firstLocation = selectedCustomerFullData?.companyContactProfiles?.[0]?.roleAssignments?.nodes?.[0]
      if (firstLocation) {
        setSelectedLocation(firstLocation)
      }
    }
  }, [selectedCustomer])

  return (
    <div className="min-h-screen bg-gray-50 admin-dashboard">
      <AdminHeader searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      {/* <AdminNavigationTabs /> */}

      <div className="flex">
        <CustomerSidebar
          customers={displayCustomers}
          selectedCustomer={selectedCustomer}
          onCustomerSelect={setSelectedCustomer}
          onCustomerInspect={onCustomerInspect}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isLoadingCustomers={isLoadingCustomers}
        />

        <div className="flex-1 p-6">
          {isLoadingClientStats ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            selectedCustomerData && (
              <>
                <CustomerOverview customer={selectedCustomerData} />
                <CustomerLocationSelector 
                  locations={selectedCustomerFullData?.companyContactProfiles?.[0]?.roleAssignments?.nodes || []}
                  onLocationSelect={setSelectedLocation}
                  selectedLocation={selectedLocation}
                />
                <CustomerKPICards customer={selectedCustomerData} clientStats={clientStats} />
                <RecentActivity activities={recentActivities} />

                {/* <div className="flex gap-6">
                  <div className="flex-1">
                    <RecentActivity activities={recentActivities} />
                  </div>
                  <div className="w-[400px]">
                    <QuickActions
                      actions={quickActions}
                      aiQuery={aiQuery}
                      onAiQueryChange={setAiQuery}
                      customerName={selectedCustomerData.name}
                    />
                  </div>
                </div> */}
              </>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
