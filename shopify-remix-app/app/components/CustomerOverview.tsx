import React from "react";
import { Customer } from "./AdminDashboard";

interface CustomerOverviewProps {
  customer: Customer;
}

export const CustomerOverview: React.FC<CustomerOverviewProps> = ({
  customer,
}) => {
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .slice(0, 2);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-teal-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-xl font-bold">
              {getInitials(customer.name)}
            </span>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {customer.name}
            </h1>
            <p className="text-gray-600">
              Account Manager: {customer.manager} | Customer ID: #{customer.id}
            </p>
            <div className="flex items-center space-x-4 mt-2">
              {customer.status === "ENABLED" ? (
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium status-active">
                  Active Account
                </span>
              ) : (
                <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium status-inactive">
                  {customer.status}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
