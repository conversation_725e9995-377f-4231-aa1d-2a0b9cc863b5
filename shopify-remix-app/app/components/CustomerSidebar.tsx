import React from "react";
import { Customer } from "./AdminDashboard";

interface CustomerSidebarProps {
  customers: Customer[];
  selectedCustomer: string;
  onCustomerSelect: (customerId: string) => void;
  onCustomerInspect: (customerId: string) => void;
  searchTerm: string;
  setSearchTerm: (searchTerm: string) => void;
  isLoadingCustomers?: boolean;
}

export const CustomerSidebar: React.FC<CustomerSidebarProps> = ({
  customers,
  selectedCustomer,
  onCustomerSelect,
  onCustomerInspect,
  searchTerm,
  setSearchTerm,
  isLoadingCustomers,
}) => {
  return (
    <div className="w-80 bg-white border-r border-gray-200 h-screen overflow-y-auto">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          Select Customer Account
        </h2>

        <div className="relative mb-4">
          <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input
            type="text"
            placeholder="Search customers..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 search-input"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="space-y-2">
          {isLoadingCustomers ? (
            <div className="p-4 rounded-lg border-2 border-gray-200 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ) : (
            customers.map((customer) => (
              <div
                key={customer.id}
                onClick={() => onCustomerSelect(customer.id)}
                className={`p-4 rounded-lg cursor-pointer transition-colors customer-card ${selectedCustomer === customer.id
                    ? "bg-blue-50 border-2 border-blue-200"
                    : "hover:bg-gray-50 border-2 border-transparent"
                  }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-800">{customer.name}</h3>
                  <div className="text-right">
                    {/* <div className="font-semibold text-gray-800">
                      {customer.value}
                    </div> */}
                    <span
                      className={`text-xs px-2 py-1 rounded ${customer.status === "ENABLED"
                          ? "bg-green-100 text-green-800 status-active"
                          : "bg-gray-100 text-gray-600 status-inactive"
                        }`}
                    >
                      {customer.status}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{customer.manager}</p>
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onCustomerInspect(customer.id);
                    }}
                    className="w-full cursor-pointer bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors duration-200 flex items-center justify-center space-x-2"
                  >
                    <i className="fas fa-search text-xs"></i>
                    <span>Inspect</span>
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
