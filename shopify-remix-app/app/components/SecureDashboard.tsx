import { useEffect, useState } from 'react';
import { useLoaderData } from '@remix-run/react';
import Dashboard from './Dashboard';
import type { ICustomer } from 'app/types/shopify-data';

interface SecureSessionData {
  loggedInCustomerId: string;
  shop: string;
  timestamp: string;
  signature: string;
  pathPrefix?: string;
  locationId?: string;
  expiresAt: number;
}

interface LoaderData {
  shop: string;
  loggedInCustomerId?: string;
  pathPrefix?: string;
  timestamp?: string;
  signature?: string;
  customer?: ICustomer;
  locationId?: string;
  shopifyLoginUrl: string;
}

const STORAGE_KEY = 'shopify_dashboard_session';
const SESSION_DURATION = 30 * 60 * 1000; // 30 minutes

export default function SecureDashboard() {
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  const loaderData = useLoaderData<LoaderData>();
  const [sessionData, setSessionData] = useState<SecureSessionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleSession = () => {
      if (loaderData.loggedInCustomerId && loaderData.timestamp && loaderData.signature) {
        
        const newSessionData: SecureSessionData = {
          loggedInCustomerId: loaderData.loggedInCustomerId,
          shop: loaderData.shop,
          timestamp: loaderData.timestamp,
          signature: loaderData.signature,
          pathPrefix: loaderData.pathPrefix,
          locationId: loaderData.locationId,
          expiresAt: Date.now() + SESSION_DURATION
        };

        localStorage.setItem(STORAGE_KEY, JSON.stringify(newSessionData));
        setSessionData(newSessionData);

        const cleanUrl = new URL(window.location.href);
        cleanUrl.search = '';
        window.history.replaceState({}, '', cleanUrl.toString());
        
        setIsLoading(false);
        return;
      }

      const storedSession = localStorage.getItem(STORAGE_KEY);
      console.log('storedSession', storedSession);
      
      if (storedSession) {
        try {
          const parsedSession: SecureSessionData = JSON.parse(storedSession);
          
          if (Date.now() > parsedSession.expiresAt) {
            console.log('Session expired, clearing localStorage');
            localStorage.removeItem(STORAGE_KEY);
            setError('Session expired. Please log in again.');
            setIsLoading(false);
            return;
          }

          console.log('Valid session found in localStorage');
          setSessionData(parsedSession);
          setIsLoading(false);
          return;
        } catch (e) {
          console.error('Error parsing stored session:', e);
          localStorage.removeItem(STORAGE_KEY);
        }
      }

      console.log('No valid session, redirecting to Shopify login');
      setError('No valid session found. Redirecting to login...');
      
      setTimeout(() => {
        window.location.href = loaderData.shopifyLoginUrl;
      }, 3000);
      
      setIsLoading(false);
    };

    handleSession();
  }, [loaderData]);

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column'
      }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          border: '4px solid #f3f3f3', 
          borderTop: '4px solid #007cba', 
          borderRadius: '50%', 
          animation: 'spin 1s linear infinite',
          marginBottom: '20px'
        }}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        textAlign: 'center',
        padding: '20px'
      }}>
        <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>Access Denied</h2>
        <p style={{ marginBottom: '30px', maxWidth: '500px' }}>{error}</p>
        
        <div style={{ display: 'flex', gap: '15px', flexWrap: 'wrap', justifyContent: 'center' }}>
          <a 
            href={loaderData.shopifyLoginUrl}
            style={{
              background: '#007cba',
              color: 'white',
              padding: '15px 30px',
              textDecoration: 'none',
              borderRadius: '5px',
              fontWeight: 'bold'
            }}
          >
            Go to Shopify Login
          </a>
          
          <button
            onClick={() => {
              localStorage.removeItem(STORAGE_KEY);
              window.location.reload();
            }}
            style={{
              background: '#6c757d',
              color: 'white',
              padding: '15px 30px',
              border: 'none',
              borderRadius: '5px',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}
          >
            Clear Session & Retry
          </button>
        </div>
        
        <p style={{ marginTop: '20px', fontSize: '0.9rem', color: '#666' }}>
          If you opened this link in a new tab or it was shared with you, 
          please access the dashboard through your Shopify store.
        </p>
      </div>
    );
  }

  if (sessionData) {
    const dashboardData = {
      shop: sessionData.shop,
      loggedInCustomerId: sessionData.loggedInCustomerId,
      pathPrefix: sessionData.pathPrefix,
      timestamp: sessionData.timestamp,
      signature: sessionData.signature,
      customer: loaderData.customer,
      locationId: sessionData.locationId
    };

    return <Dashboard customer={loaderData.customer} />;
  }

  return null;
}
