import type { ActionFunctionArgs, HeadersFunction, LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, useRouteError, useLoaderData, useFetcher } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";

import { authenticate } from "../shopify.server";
import AdminDashboard from "../components/AdminDashboard";
import { useEffect, useState } from "react";
import { useCustomerFetcher } from "app/hooks/useCustomerActions";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return { 
    apiKey: process.env.SHOPIFY_API_KEY || "",
    appUrl: process.env.SHOPIFY_APP_URL || ""
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  const formData = await request.formData();
  const action = formData.get("action") as string;

  const customerService = new (await import("../services/customerService")).CustomerService(admin);

  switch (action) {
    case "searchCustomers":
      const searchTerm = formData.get("searchTerm") as string;
      const limit = parseInt(formData.get("limit") as string) || 50;
      const cursor = formData.get("cursor") as string;
      return await customerService.searchCustomers({
        query: searchTerm,
        first: limit,
        after: cursor
      });

    default:
      return { error: "Invalid action" };
  }
};

export default function App() {
  const { apiKey, appUrl } = useLoaderData<typeof loader>();
  const { fetchCustomers, loading, data } = useCustomerFetcher();
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    setTimeout(() => {
      fetchCustomers("", 50);
    }, 400);
  }, []);

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      fetchCustomers(searchTerm, 50);
    }, 500);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm])

  console.log("data", data)

  const onCustomerInspect = (customerId: string) => {
    const customer = data?.customers?.find(c => c.id === customerId);
    if (!customer) return;

    const currentUrl = window.location.href;
    const urlParams = new URLSearchParams(window.location.search);
    const shop = urlParams.get('shop') || 
                 currentUrl.match(/myshopify\.com/)?.[0] || 
                 'store.myshopify.com';
    const bareCustomerId = customerId.split('/').pop(); 
    const dashboardUrl = `${appUrl || window.location.origin}/dashboard?shop=${shop}&logged_in_customer_id=${bareCustomerId}`;
    
    console.log("Opening dashboard URL:", dashboardUrl);
    
    window.open(dashboardUrl, '_blank');
  }

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <AdminDashboard
        customers={data?.customers || []}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        isLoadingCustomers={loading}
        onCustomerInspect={onCustomerInspect}
      />
      <Outlet />
    </AppProvider>
  );
}

export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
