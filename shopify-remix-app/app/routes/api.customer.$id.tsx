import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { GET_CUSTOMER_BY_ID } from "app/graphql";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const { id } = params;
  
  if (!id) {
    return json({ error: "Customer ID is required" }, { status: 400 });
  }

  try {
    const gid = `gid://shopify/Customer/${id}`;
    const shop = 'maintenance-supply.myshopify.com';
    const storefrontAccessToken = process.env.SHOPIFY_STOREFRONT_API_TOKEN || '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';

    const resp = await fetch(`https://${shop}/admin/api/2025-07/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": storefrontAccessToken,
      },
      body: JSON.stringify({
        query: GET_CUSTOMER_BY_ID,
        variables: {
          id: gid,
        },
      }),
    });

    const data = await resp.json();
    console.log("Customer API response:", JSON.stringify(data));

    if (data.data?.customer) {
      return json({ 
        success: true, 
        customer: data.data.customer 
      });
    } else if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      return json({ 
        success: false, 
        error: "Failed to fetch customer data",
        details: data.errors 
      }, { status: 500 });
    } else {
      return json({ 
        success: false, 
        error: "Customer not found" 
      }, { status: 404 });
    }
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
};
