import { useFetcher } from "@remix-run/react";
import { ClientStatsData } from "app/types/dashboard";
import type { ICustomer } from "app/types/shopify-data";
import { useCallback, useEffect, useState } from "react";

export type TimeRange = 'last_week' | 'last_month';
export type ReportType = 'site' | 'sir';
export interface ReportParams {
  reportType: ReportType;
  timeRange: TimeRange;
  clientCode?: string;
  clientName?: string;
  filterLocations?: string[];
}
export function useCustomerFetcher() {
  const fetcher = useFetcher();

  const fetchCustomers = async (searchTerm = "", limit: number = 50, cursor: string = "") => {
    fetcher.submit(
      {
        action: "searchCustomers",
        searchTerm,
        limit: limit.toString(),
        cursor
      },
      { method: "POST" }
    );
  };

  return {
    fetchCustomers,
    loading: fetcher.state === "submitting",
    data: fetcher.data as {
      customers: ICustomer[];
      pageInfo: {
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        startCursor: string;
        endCursor: string;
      };
      success?: boolean;
      error?: string;
    }
  };
}

export const useCustomerById = (id: string) => {
  const fetcher = useFetcher();

  useEffect(() => {
    if (!id) return;
    fetcher.submit({
      action: "getCustomerById",
      id
    }, {
      method: "POST"
    });
  }, [id]);

  return {
    loading: fetcher.state === "submitting",
    data: fetcher.data as {
      customer: ICustomer;
      success?: boolean;
      error?: string;
    }
  };
};

export const useReportGenerator = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generateReport = async (params: ReportParams) => {
    setIsGenerating(true);

    try {
      const now = new Date();
      let postingDateFrom: string;
      let postingDateTo: string;

      if (params.timeRange === 'last_week') {
        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        postingDateFrom = lastWeek.toISOString().split('T')[0];
        postingDateTo = now.toISOString().split('T')[0];
      } else {
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        postingDateFrom = lastMonth.toISOString().split('T')[0];
        postingDateTo = endOfLastMonth.toISOString().split('T')[0];
      }

      const apiUrl = params.reportType === 'site'
        ? 'https://msc.personify.tech/api/bc/gen-site-report'
        : 'https://msc.personify.tech/api/bc/gen-sir-report';

      const requestBody = params.reportType === 'site'
        ? {
          clientName: params.clientName,
          postingDateFrom,
          postingDateTo,
          filterLocations: params.filterLocations
        }
        : {
          client: params.clientCode,
          postingDateFrom,
          postingDateTo,
          filterLocations: params.filterLocations
        };

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'x-api-key': 'kgmdqtveva',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      const filename = params.reportType === 'site'
        ? `site-report-${params.clientName}-${postingDateFrom}-${postingDateTo}.xlsx`
        : `sir-report-${params.clientCode}-${postingDateFrom}-${postingDateTo}.xlsx`;

      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return { success: true };
    } catch (error: any) {
      console.error('Error generating report:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generateReport,
    isGenerating
  };
};

export const useClientStats = (customer?: ICustomer, filterLocations?: string[]) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<ClientStatsData | null>(null);

  const companyExternalId = customer?.companyContactProfiles?.[0]?.company?.externalId

  useEffect(() => {
    console.log("filterLocations", filterLocations)
    console.log("customer", customer)
    if (!companyExternalId || !filterLocations || filterLocations.length === 0) {
      setData(null);
      return;
    }

    const getReport = async () => {
      setIsLoading(true);

      try {
        const now = new Date();
        let postingDateFrom: string;
        let postingDateTo: string;

        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        postingDateFrom = lastMonth.toISOString().split('T')[0];
        postingDateTo = endOfLastMonth.toISOString().split('T')[0];

        const apiUrl = 'https://msc.personify.tech/api/bc/stats';

        const requestBody = {
          client: customer.companyContactProfiles?.[0]?.company?.externalId,
          filterLocations: filterLocations || [],
          postingDateFrom,
          postingDateTo
        };

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'x-api-key': 'kgmdqtveva',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        }); 

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("statsdata", data?.data)
        setData(data?.data);
      } catch (error: any) {
        console.error('statsdata Error generating report:', error);
      } finally {
        setIsLoading(false);
      }
    };

    getReport();
  }, [companyExternalId, JSON.stringify(filterLocations)]);

  return {
    data,
    isLoading
  };
};
