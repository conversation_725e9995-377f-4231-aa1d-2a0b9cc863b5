import { useLocation } from "../contexts/LocationContext";

export const useLocationForReports = () => {
  const { selectedLocation } = useLocation();
  
  const getFilterLocations = () => {
    if (!selectedLocation?.externalId) {
      return undefined;
    }
    return [selectedLocation.externalId];
  };

  return {
    selectedLocation,
    filterLocations: getFilterLocations(),
    hasLocation: !!selectedLocation?.externalId
  };
};
