import React, { createContext, useContext, useState, ReactNode } from 'react';

interface Location {
  id: string;
  name: string;
  externalId: string;
}

interface LocationContextType {
  selectedLocation: Location | null;
  setSelectedLocation: (location: Location | null) => void;
  locationId?: string | null;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export const useLocation = () => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

interface LocationProviderProps {
  children: ReactNode;
  locationId?: string | null;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({ children, locationId }) => {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);

  return (
    <LocationContext.Provider value={{ selectedLocation, setSelectedLocation, locationId }}>
      {children}
    </LocationContext.Provider>
  );
};
