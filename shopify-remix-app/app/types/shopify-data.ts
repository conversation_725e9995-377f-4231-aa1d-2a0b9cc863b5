export interface RoleAssignmentItem {
  id: string;
  company: {
    id: string;
    name: string;
    externalId: string;
  };
  companyLocation: {
    id: string;
    name: string;
    externalId: string | null;
  };
  role: {
    name: string;
  };
}

export interface ICustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  companyContactProfiles: {
    company: {
      id: string;
      name: string;
      externalId: string;
      locations: {
        nodes: {
          id: string;
          name: string;
          externalId: string;
        }[];
      };
    };
    roleAssignments: {
      nodes: Array<RoleAssignmentItem>
    }
  }[];
  displayName: string;
  amountSpent: {
    amount: string;
    currencyCode: string;
  };
  createdAt: string;
  lifetimeDuration: string;
  numberOfOrders: string;
  state: string;
  events: {
    nodes: {
      action: string;
      appTitle: string;
      attributeToApp: boolean;
      attributeToUser: boolean;
      createdAt: string;
      criticalAlert: boolean;
      id: string;
      message: string;
    }[];
  };
}
