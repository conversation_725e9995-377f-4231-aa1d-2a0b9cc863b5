import type { HeadersFunction, LinksFunction } from "@remix-run/node";
import {
  <PERSON>s,
  Meta,
  Outlet,
  Scrip<PERSON>,
  ScrollRestoration,
  useRouteError,
} from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import "./styles/app.css";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />

        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossOrigin="anonymous" referrerPolicy="no-referrer"></script>
        <script src="https://code.highcharts.com/highcharts.js"></script>
        <script dangerouslySetInnerHTML={{
          __html: `
            window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};
          `
        }} />
        <style dangerouslySetInnerHTML={{
          __html: `
            body {
              font-family: 'Inter', sans-serif !important;
            }
            .fa, .fas, .far, .fal, .fab {
              font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
            }
            ::-webkit-scrollbar {
              display: none;
            }
            html, body {
              -ms-overflow-style: none;
              scrollbar-width: none;
            }
          `
        }} />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers: HeadersFunction = (headersArgs) => {
  const headers = boundary.headers(headersArgs);

  // Remove X-Frame-Options header to allow embedding
  headers.delete("X-Frame-Options");

  headers.set("Content-Security-Policy", "frame-ancestors *");

  // Add headers to allow embedding
  headers.set("X-Content-Type-Options", "nosniff");
  headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  return headers;
};