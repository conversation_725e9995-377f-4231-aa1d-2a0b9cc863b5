# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "62da22a8a94efbc74a91df10a9cbe5c5"
name = "MSC BI Report"
handle = "msc-bi-report"
application_url = "https://msc-bi-report.vercel.app"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "msc-teleorder-test.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = ["https://msc-bi-report.vercel.app/auth/callback", "https://msc-bi-report.vercel.app/auth/shopify/callback", "https://msc-bi-report.vercel.app/api/auth/callback"]

[webhooks]
api_version = "2025-07"

[app_proxy]
url = "https://msc-bi-report.vercel.app/dashboard"
subpath = "dashboard"
prefix = "apps"

[pos]
embedded = false
