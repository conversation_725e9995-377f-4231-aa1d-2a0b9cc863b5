{%- liquid
  assign customer_id = customer.id | default: ''
  assign customer_location_id = customer.current_location.id | default: ''
  
  # Fallback: try to get location from addresses if current_location is not available
  if customer_location_id == blank and customer.addresses.size > 0
    assign customer_location_id = customer.addresses.first.id
  endif
  
  assign shop_domain = shop.domain
-%}

<script>
  console.log('=== BI Dashboard - Customer Info ===');
  
  // Basic customer checks
  console.log('Customer exists:', {% if customer %}true{% else %}false{% endif %});
  console.log('Final customer_id for iframe:', {{ customer_id | json }});
  console.log('Final location_id for iframe:', {{ customer_location_id | json }});
  console.log('=== End Customer Info ===');
</script>

{%- if customer and customer.id != blank -%}
  <div class="section relative section--full-width section--padded-default"{% if settings.animations_enabled != "disabled" and section.settings.prevent_animation == false %} data-cc-animate{% endif %}>
            <div class="bi-dashboard-container" style="width: 100%; height: 100vh; border: none;">
          <iframe 
            src="https://msc-bi-report.vercel.app/dashboard?customer_id={{ customer_id }}&location_id={{ customer_location_id }}&shop=maintenance-supply.myshopify.com&logged_in_customer_id={{ customer_id }}&redirected=1"
            style="width: 100%; height: 100vh; border: none;" 
            frameborder="0" 
            allowfullscreen
            title="BI Dashboard">
          </iframe>
        </div>
  </div>
{%- else -%}
  <div class="section relative section--padded-default">
    <div class="container">
      <div class="text-center py-8">
        <h2>BI Dashboard</h2>
        <p>Loading dashboard...</p>
        <div class="bi-dashboard-container" style="width: 100%; height: 80vh; border: none;">
          <iframe 
            src="https://msc-bi-report.vercel.app/dashboard?shop=maintenance-supply.myshopify.com&redirected=1"
            style="width: 100%; height: 80vh; border: none;" 
            frameborder="0" 
            allowfullscreen
            title="BI Dashboard">
          </iframe>
        </div>
        <p class="mt-4 text-sm text-gray-600">Note: Some features may be limited when not logged in.</p>
        <a href="{{ routes.account_login_url }}" class="btn btn--primary mt-4">Log In for Full Access</a>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "BI Dashboard Iframe",
  "class": "cc-bi-dashboard-iframe",
  "settings": [
    
  ]
}
{% endschema %}
