{{ 'products-grid.css' | asset_url | stylesheet_tag }}
{{ 'product.css' | asset_url | stylesheet_tag }}
<script src="{{ 'products-toolbar.js' | asset_url }}" defer="defer"></script>

{%- if settings.card_hover_action == 'slideshow' -%}
  <script src="{{ 'product-card-image-slider.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if settings.enable_quick_add -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'variant-picker.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'custom-select.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if settings.pagination_style == "modern" or settings.pagination_infinite -%}
  <script src="{{ 'custom-pagination.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if section.blocks.size > 0 -%}
  {{ 'promos.css' | asset_url | stylesheet_tag }}

  {%- for block in section.blocks -%}
    {%- if block.settings.video_shopify != blank -%}
      {{ 'video.css' | asset_url | stylesheet_tag }}
      <script src="{{ 'video.js' | asset_url }}" defer="defer"></script>
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}
{%- comment -%}
  ABL Custom Product Listing via metafield (collection.metafields.custom.approved_products)
  Renders a paginated product grid using handles from metafield.
{%- endcomment -%}
{% if customer.current_location.metafields.custom.approved_products %}

  <div class="container">
<div class="abl-collection-products">
   <div class="mb-4 text-center">
          <p>Showing {{ total_items }} products available for your location</p>
        </div>
        
        <div class="budget-filter mb-16 md:mb-6 text-center md:flex md:justify-end md:items-center ">
          <label for="budget-category-filter" class="block mb-2 md:mr-2 font-medium">Filter by Budget Category:</label>
          <select id="budget-category-filter" class="input input--select max-w-xs text-center">
            <option value="">All Categories</option>
            <option value="materials">Materials</option>
            <option value="consumables">Consumables</option>
            <option value="machinery">Machinery</option>
            <option value="workwear">Workwear</option>
            <option value="miscellaneous">Miscellaneous</option>
          </select>
        </div>
        
        <div class="main-products-grid{% if settings.card_contain %} main-products-grid--contained{% endif %} flex" data-layout="{{ section.settings.product_display_mode }}">
      
      <div class="main-products-grid__results relative flex-auto" id="filter-results">
  <ul id="product-grid" role="list" class="grid mb-8 md:mb-12 grid-cols-2 sm:grid-cols-3 xl:grid-cols-4 gap-x-theme gap-y-8"></ul>
      </div>
    </div>
  <div class="abl-pagination-controls text-center my-6 mb-6">
    <button id="prev" type="button" class="btn btn--secondary mr-2">Previous</button>
    <span>Page <span id="page-number">1</span></span>
    <button id="next" type="button" class="btn btn--secondary ml-2">Next</button>
  </div>
</div>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    console.log("ABL Collection script loaded");

    let handles = [];
    let allProducts = [];
    let filteredProducts = [];
    let currentCategory = '';
    
    try {
      const handleMap = {{ customer.current_location.metafields.custom.approved_products.value | json }};
      const locationCode = {{customer.current_location.external_id | json }};
      const rawMetafield = {{ customer.current_location.metafields.custom.approved_products | json }};
      
      console.log("=== ABL DEBUG - COMPARING WITH GRAPHQL ===");
      console.log("Customer location external ID:", locationCode);
      console.log("Raw metafield object:", rawMetafield);
      console.log("Metafield value (parsed):", handleMap);
      console.log("Metafield type:", typeof handleMap);
      console.log("Is array:", Array.isArray(handleMap));
      console.log("Is object:", typeof handleMap === 'object' && handleMap !== null);
      
      if (handleMap && handleMap[locationCode]) {
        handles = handleMap[locationCode];
      } else {
        console.warn("No product handles found for location:", locationCode);
        console.log("Available keys in handleMap:", Object.keys(handleMap || {}));
        return;
      }
      if (!Array.isArray(handles)) {
        throw new Error("Parsed product handles for location are not an array");
      }
      console.log("Loaded handles:", handles);
      console.log(`Total handles available: ${handles.length}`, handles);

      async function loadAndValidateProducts(handles) {
        const productDatas = await Promise.all(
          handles.map(async handle => {
            try {
              const res = await fetch(`/products/${handle}.js`);
              return res.ok ? res.json() : null;
            } catch {
              return null;
            }
          })
        );

        let res = await fetch(`https://msc.personify.tech/api/synced-products/handle`, {
          method: 'post',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ handles })
        })
        res = await res.json();
        return res.data.map(i => ({
          ...i,
          ...(productDatas.find(j => j?.handle === i.handle) || {})
        }))
      }

      // Load all products directly (no separate validation step)
      loadAndValidateProducts(handles).then(async (products) => {
        allProducts = products
        filteredProducts = [...products];
        
        renderProducts(1);
      });

      // Prevent initial render until filtering is done:
    } catch (e) {
      console.error("Failed to load handles from metafield", e);
      return;
    }

    const productsPerPage = 20;
    let currentPage = 1;

    function formatMoney(cents) {
      return '£' + (cents / 100).toFixed(2);
    }

    // Filter products by budget category
    function filterByCategory(category) {
      if (!category) {
        filteredProducts = [...allProducts];
      } else {
        filteredProducts = allProducts.filter(product => {
          const productCategory = product.budgetCategory?.toLowerCase();
          return productCategory === category.toLowerCase();
        });
      }
      
      // Reset to first page when filtering
      currentPage = 1;
      renderProducts(1);
      
      // Update product count display
      const countDisplay = document.querySelector('.abl-collection-products .mb-4 p');
      if (countDisplay) {
        countDisplay.textContent = `Showing ${filteredProducts.length} products available for your location`;
      }
    }

    function renderProducts(page) {
      const start = (page - 1) * productsPerPage;
      const end = start + productsPerPage;
      const pageProducts = filteredProducts.slice(start, end);

      const container = document.getElementById("product-grid");
      if (!container) {
        console.warn("Product grid container not found.");
        return;
      }

      container.innerHTML = "";

      pageProducts.forEach(product => {
        const html = `
  <li class="js-pagination-result" style="list-style: none;">
    <product-card class="card card--product h-full card--no-lines card--product-compare relative flex">
      
      <div class="card__media relative">
        <a href="${product.url}" aria-label="${product.title}" class="media block relative js-prod-link" style="padding-top: 100%;" tabindex="-1">
          ${product.featured_image ? `
            <img src="${product.featured_image}" class="img-fit card__main-image" loading="lazy" alt="${product.title}">
          ` : `
            <svg class="media__placeholder img-fit" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 525.5 525.5"><path d="M324.5 212.7H203c-1.6 0-2.8 1.3-2.8 2.8V308c0 1.6 1.3 2.8 2.8 2.8h121.6c1.6 0 2.8-1.3 2.8-2.8v-92.5c0-1.6-1.3-2.8-2.9-2.8zm1.1 95.3c0 .6-.5 1.1-1.1 1.1H203c-.6 0-1.1-.5-1.1-1.1v-92.5c0-.6.5-1.1 1.1-1.1h121.6c.6 0 1.1.5 1.1 1.1V308z"></path><path d="M210.4 299.5H240v.1s.1 0 .2-.1h75.2v-76.2h-105v76.2zm1.8-7.2l20-20c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l1.5 1.5 16.8 16.8c-12.9 3.3-20.7 6.3-22.8 7.2h-27.7v-5.5zm101.5-10.1c-20.1 1.7-36.7 4.8-49.1 7.9l-16.9-16.9 26.3-26.3c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l27.5 27.5v7.8zm-68.9 15.5c9.7-3.5 33.9-10.9 68.9-13.8v13.8h-68.9zm68.9-72.7v46.8l-26.2-26.2c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-26.3 26.3-.9-.9c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-18.8 18.8V225h101.4z"></path><path d="M232.8 254c4.6 0 8.3-3.7 8.3-8.3s-3.7-8.3-8.3-8.3-8.3 3.7-8.3 8.3 3.7 8.3 8.3 8.3zm0-14.9c3.6 0 6.6 2.9 6.6 6.6s-2.9 6.6-6.6 6.6-6.6-2.9-6.6-6.6 3-6.6 6.6-6.6z"></path></svg>
          `}
        </a>
      </div>
      <div class="card__info-container flex flex-col flex-auto relative">
        <div class="card__info w-full">
          <div class="card__info-inner inline-block w-full">
            <p class="card__title font-bold mt-1 mb-0">
              <a href="${product.url}" class="card-link text-current js-prod-link">${product.title}</a>
            </p>
            <p class="text-sm text-sku">SKU: ${product.variants[0].sku}</p>
            <p class="card__weight text-sm mb-1 mt-1">${(product.variants[0].weight / 1000).toFixed(2)} kg</p>
            <div class="price price--top">
              <div class="price__default">
                <strong class="price__current">${formatMoney(product.price)}</strong>
              </div>
            </div>
            ${product.budgetCategory ? `
              <p class="text-xs text-gray-600 mt-1">Category: ${product.budgetCategory}</p>
            ` : ''}
          </div>
        </div>
        <div class="card__quick-add mob:card__quick-add--below">
          <product-form>
            <form method="post" action="/cart/add" id="quick-add-${product.id}" accept-charset="UTF-8" class="js-product-form" enctype="multipart/form-data" novalidate>
              <input type="hidden" name="form_type" value="product">
              <input type="hidden" name="utf8" value="✓">
              <input type="hidden" name="id" value="${product.variants[0].id}">
              <label for="quantity-${product.id}" class="visually-hidden">Quantity</label>
              <input type="number" id="quantity-${product.id}" name="quantity" value="1" min="1" class="input input--quantity" aria-label="Quantity">
              <button class="btn btn--primary w-full" name="add" aria-haspopup="dialog">
                <span class="quick-add-btn-icon">
                  <span class="visually-hidden">Add to cart</span>
                  <svg width="24" height="24" fill="currentColor" class="icon"><path d="M12.12 20.44H5.6V9.56h12.8v3.73c.*********.8.7.44 0 .8-.35.8-.8v-4.5a.792.792 0 0 0-.8-.69H17V6.5C16.9 4 14.7 2 12 2S7 4.09 7 6.67V8H4.71c-.4.04-.71.37-.71.78v12.53a.8.8 0 0 0 .8.69h7.43c.38-.06.67-.39.67-.78 0-.43-.35-.78-.78-.78ZM8.66 6.67c0-1.72 1.49-3.11 3.33-3.11s3.33 1.39 3.33 3.11V8H8.65V6.67Z"></path><path d="M20 17.25h-2.4v-2.5a.817.817 0 0 0-.8-.7c-.44 0-.8.36-.8.8v2.4h-2.5c-.4.06-.7.4-.7.8 0 .44.36.8.8.8H16v2.5c.06.4.4.7.8.7.44 0 .8-.36.8-.8v-2.4h2.5c.4-.06.69-.4.7-.8 0-.44-.35-.8-.8-.8Z"></path></svg>
                </span>
                <span class="quick-add-btn-text">Add to cart</span>
              </button>
              <input type="hidden" name="product-id" value="${product.id}">
            </form>
          </product-form>
        </div>
      </div>
    </product-card>
  </li>`;
        container.insertAdjacentHTML("beforeend", html);
      });

      const pageDisplay = document.getElementById("page-number");
      if (pageDisplay) {
        pageDisplay.textContent = page;
      }
    }

    // Add event listener for budget category filter
    document.getElementById("budget-category-filter").addEventListener("change", function(e) {
      filterByCategory(e.target.value);
    });

    document.getElementById("next").addEventListener("click", () => {
      if ((currentPage * productsPerPage) < filteredProducts.length) {
        currentPage++;
        renderProducts(currentPage);
      }
    });

    document.getElementById("prev").addEventListener("click", () => {
      if (currentPage > 1) {
        currentPage--;
        renderProducts(currentPage);
      }
    });

    // renderProducts(currentPage);
  });
</script>
{% endif %}

{%- liquid
  if section.settings.card_size_mobile == 'small'
    assign grid_mobile_cols = 'grid-cols-2 small-cards-mobile'
  else
    assign grid_mobile_cols = 'grid-cols-1'
  endif

  if collection.products.size > 0
    if section.settings.card_size == 'small'
      assign grid_classes_filters_open = grid_mobile_cols | append: ' sm:grid-cols-3 xl:grid-cols-4 small-cards-desktop'
      assign grid_classes_filters_closed = grid_mobile_cols | append: ' sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 small-cards-desktop'
    elsif section.settings.card_size == 'medium'
      assign grid_classes_filters_open = grid_mobile_cols | append: ' sm:grid-cols-3'
      assign grid_classes_filters_closed = grid_mobile_cols | append: ' sm:grid-cols-3 xl:grid-cols-4'
    else
      assign grid_classes_filters_open = grid_mobile_cols | append: ' sm:grid-cols-2 xl:grid-cols-3'
      assign grid_classes_filters_closed = grid_mobile_cols | append: ' sm:grid-cols-2 xl:grid-cols-3'
    endif

    if settings.prod_card_image_ratio == 'shortest'
      assign image_ratio = 0
      for product in collection.products
        if product.featured_media.preview_image.aspect_ratio > image_ratio
          assign image_ratio = product.featured_media.preview_image.aspect_ratio
        endif
      endfor
    elsif settings.prod_card_image_ratio == 'tallest'
      assign image_ratio = 99
      for product in collection.products
        if product.featured_media.preview_image.aspect_ratio < image_ratio
          assign image_ratio = product.featured_media.preview_image.aspect_ratio
        endif
      endfor
    else
      assign image_ratio = settings.prod_card_image_ratio
    endif
  endif

  assign compare_toggle = false
  assign compare_visible = false
  if settings.enable_compare
    if settings.compare_toggle == "none" or settings.compare_toggle == "toggle_on"
      assign compare_toggle = true
    endif

    if settings.compare_toggle != "none"
      assign compare_visible = true
    endif
  endif
-%}

  {%- if section.settings.enable_filtering or section.settings.show_layout_toggle or section.settings.enable_sorting or compare_visible -%}
    <script src="{{ 'products-toolbar.js' | asset_url }}" defer="defer"></script>
    {% unless collection.handle == 'approved-buyers-list' %}
      {% render 'products-toolbar', results: collection %}
    {% endunless %}
  {%- endif -%}

  {%- comment -%}PRODUCT FILTERING LOGIC{%- endcomment -%}
  {%- liquid
    # Check if customer has location restrictions
    assign has_restrictions = false
    assign allowed_handles = array
    
    if customer and customer.current_location
      assign allowed_products_json = customer.current_location.metafields.custom.approved_products
      
      if allowed_products_json
        # Try to parse the JSON - simpler approach
        assign json_string = allowed_products_json | strip
        
        # Check if it looks like valid JSON with an array
        if json_string contains '[' and json_string contains ']'
          # Just extract what's between the brackets and clean it up
          assign start_bracket = json_string | split: '[' | last
          assign array_content = start_bracket | split: ']' | first
          assign clean_content = array_content | replace: '"', '' | strip
          assign allowed_handles = clean_content | split: ','
          
          # Set restrictions flag only if we got at least one handle
          if allowed_handles.size > 0
            assign has_restrictions = true
          endif
        endif
        
        # If JSON parsing failed or empty array, show normal collection (no restrictions)
        if allowed_handles.size == 0
          assign has_restrictions = false
        endif
        
        # Manual pagination for restricted products
        assign items_per_page = 20
        assign current_page = 1
        if request.page_type == 'collection' and request.parameters.page != blank
          assign current_page = request.parameters.page | plus: 0
        endif
        
        assign offset = items_per_page | times: current_page | minus: items_per_page
        assign total_items = allowed_handles.size
        assign total_pages = total_items | divided_by: items_per_page
        if total_pages == 0 and total_items > 0
          assign total_pages = 1
        endif
        assign paginated_handles = allowed_handles | slice: offset, items_per_page
      endif
    endif
  -%}

  {%- if collection.handle == 'approved-buyers-list' and has_restrictions -%}
    {%- comment -%}CUSTOM COLLECTION - AJAX FETCH OF ALLOWED HANDLES{%- endcomment -%}
    <div class="main-products-grid{% if settings.card_contain %} main-products-grid--contained{% endif %} flex" data-layout="{{ section.settings.product_display_mode }}">
      
      <div class="main-products-grid__results relative flex-auto" id="filter-results">

       
        <div id="buyer-list"></div>
        <script>
          (function(){
            const handles = {{ allowed_handles | json }};
            if (!handles.length) return;
            const query = handles.map(h => `handle:"${h}"`).join(' OR ');
            const url = `/search?view=custom&type=product&q=${encodeURIComponent(query)}`;

            fetch(url)
              .then(res => res.text())
              .then(html => {
                document.getElementById('buyer-list').innerHTML = html;
              })
              .catch(err => console.error('Buyer-list fetch failed', err));
          })();
        </script>
      </div>
    </div>
    
  {%- elsif has_restrictions == false -%}
    {%- comment -%}STANDARD SHOPIFY PAGINATION - NO RESTRICTIONS{%- endcomment -%}
    {%- paginate collection.products by section.settings.products_per_page -%}
      <custom-pagination class="main-products-grid{% if settings.card_contain %} main-products-grid--contained{% endif %} flex" data-layout="{{ section.settings.product_display_mode }}" data-compare="{{ compare_toggle }}" data-infinite-scroll="{{ settings.pagination_infinite }}" data-pause-infinite-scroll="false">
        {%- if collection.filters != empty and section.settings.enable_filtering -%}
        <div class="main-products-grid__filters{% if section.settings.filters_open_lg == false %} lg:js-hidden{% endif %}">
          {% render 'facet-filters', results: collection %}
        </div>
      {%- endif -%}
        <div class="main-products-grid__results relative flex-auto" id="filter-results">
          {%- if collection.products.size == 0 -%}
            <p>{{ 'sections.collection.empty' | t }}</p>
          {%- else -%}
            {%- liquid
              assign active_filters_count = 0
              if collection.filters != empty
                for filter in collection.filters
                  if filter.type == 'price_range'
                    if filter.min_value.value
                      assign active_filters_count = active_filters_count | plus: 1
                    endif
                    if filter.max_value.value
                      assign active_filters_count = active_filters_count | plus: 1
                    endif
                  else
                    assign active_filters_count = active_filters_count | plus: filter.active_values.size
                  endif
                endfor
              endif
              assign filtered_products = collection.products
            -%}

            <ul class="grid mb-8 md:mb-12 {% if section.settings.filters_open_lg %}{{ grid_classes_filters_open }}{% else %}{{ grid_classes_filters_closed }}{% endif %} gap-x-theme {% if settings.card_contain %}gap-y-5 md:gap-y-8{% else %}gap-y-16{% endif %}"
                data-filters-open-classes="grid mb-8 md:mb-12 {{ grid_classes_filters_open }} gap-x-theme {% if settings.card_contain %}gap-y-5 md:gap-y-8{% else %}gap-y-16{% endif %}"
                data-filters-closed-classes="grid mb-8 md:mb-12 {{ grid_classes_filters_closed }} gap-x-theme {% if settings.card_contain %}gap-y-5 md:gap-y-8{% else %}gap-y-16{% endif %}{% unless paginate.pages > 1 %} mb-8 md:mb-12{% endunless %}" role="list">
              {%- comment -%}Process wide promos aligned top{%- endcomment -%}
              {%- for block in section.blocks -%}
                {%- liquid
                  assign show_promo = false
                  if block.settings.position == "top"
                    if paginate.pages == 0 or paginate.current_page == 1
                      if active_filters_count == 0 or block.settings.hide_on_filter == false
                        assign show_promo = true
                      endif
                    endif
                  endif
                -%}

                {%- if show_promo -%}
                  <li class="promo-item--{{ block.settings.view }} col-span-full js-pagination-result" style="--promo-text-color: {{- block.settings.text_color -}};--promo-min-height: {{- block.settings.min_height -}}px;--promo-overlay-color: rgba({{ block.settings.tint_color.red }}, {{ block.settings.tint_color.green }}, {{ block.settings.tint_color.blue }}, {{ block.settings.tint_opacity | divided_by: 100.0 }});">
                    {%-
                      render 'promo-image',
                        link_url: block.settings.link_url,
                        content: block.settings.content,
                        text_alignment: block.settings.text_alignment,
                        image: block.settings.image,
                        video_shopify: block.settings.video_shopify,
                        text_width: block.settings.text_width,
                        text_type_scale: block.settings.text_type_scale,
                        size: section.settings.card_size,
                        button_label: block.settings.button_label,
                        button_style: block.settings.button_style
                    -%}
                  </li>
                {%- endif -%}
              {%- endfor -%}

              {%- assign rendered_product_ids = "" -%}
            
            {%- for product in filtered_products -%}
              {%- assign product_index = forloop.index -%}
              {%- for block in section.blocks -%}
                {%- liquid
                  assign show_promo = false
                  unless block.settings.position == "top" or block.settings.position == "bottom"
                    assign is_last_position = false

                    if product_index == block.settings.position or is_last_position
                      if paginate.pages == 0 or paginate.current_page == 1
                        if active_filters_count == 0 or block.settings.hide_on_filter == false
                          assign show_promo = true
                          if product_index == collection.products.size and block.settings.position > product_index
                            assign is_last_position = true
                          endif
                        endif
                      endif
                    endif
                  endunless
                -%}
                {%- if is_last_position -%}
                  {%- unless rendered_product_ids contains product.id -%}
                    {%- assign rendered_product_ids = rendered_product_ids | append: product.id | append: "," -%}
                    <li class="js-pagination-result">{% render 'product-card', product: product, collection: collection, image_ratio: image_ratio, show_compare: settings.enable_compare %}</li>
                  {%- endunless -%}
                {%- endif -%}

                {%- if show_promo -%}
                  <li class="promo-item--{{ block.settings.view }} js-pagination-result" {% if block.type == "image-promotion" %}style="--promo-text-color: {{- block.settings.text_color -}};--promo-min-height: {{- block.settings.min_height -}}px;--promo-overlay-color: rgba({{ block.settings.tint_color.red }}, {{ block.settings.tint_color.green }}, {{ block.settings.tint_color.blue }}, {{ block.settings.tint_opacity | divided_by: 100.0 }});"{% endif %}>
                    {% if block.type == "image-promotion" or block.type == "wide-promotion" %}
                      {%-
                        render 'promo-image',
                          link_url: block.settings.link_url,
                          content: block.settings.content,
                          text_alignment: block.settings.text_alignment,
                          image: block.settings.image,
                          video_shopify: block.settings.video_shopify,
                          text_width: block.settings.text_width,
                          text_type_scale: block.settings.text_type_scale,
                          size: section.settings.card_size,
                          button_label: block.settings.button_label,
                          button_style: block.settings.button_style
                      -%}
                    {%- elsif block.type == "card-promotion" -%}
                      {%-
                        render 'promo-card',
                          link_url: block.settings.link_url,
                          title: block.settings.title,
                          content: block.settings.content,
                          image: block.settings.image,
                          image_ratio: block.settings.image_ratio,
                          button_label: block.settings.button_label,
                          button_style: block.settings.button_style,
                          color_scheme: block.settings.color_scheme,
                          button_bottom_align: block.settings.button_bottom_align,
                          size: section.settings.card_size
                      -%}
                    {%- endif -%}
                  </li>
                {%- endif -%}
              {%- endfor -%}

              {%- unless is_last_position and rendered_product_ids contains product.id -%}
                {%- assign rendered_product_ids = rendered_product_ids | append: product.id | append: "," -%}
                <li class="js-pagination-result">{% render 'product-card', product: product, collection: collection, image_ratio: image_ratio, show_compare: settings.enable_compare %}</li>
              {%- endunless -%}
            {%- endfor -%}

            {%- comment -%}Process wide promos aligned bottom{%- endcomment -%}
            {%- for block in section.blocks -%}
              {%- liquid
                assign show_promo = false
                if block.settings.position == "bottom"
                  if paginate.pages == 0 or paginate.current_page == 1
                    if active_filters_count == 0 or block.settings.hide_on_filter == false
                      assign show_promo = true
                    endif
                  endif
                endif
              -%}

              {%- if show_promo -%}
                <li class="promo-item--{{ block.settings.view }} col-span-full js-pagination-result" style="--promo-text-color: {{- block.settings.text_color -}};--promo-min-height: {{- block.settings.min_height -}}px;--promo-overlay-color: rgba({{ block.settings.tint_color.red }}, {{ block.settings.tint_color.green }}, {{ block.settings.tint_color.blue }}, {{ block.settings.tint_opacity | divided_by: 100.0 }});">
                  {%-
                    render 'promo-image',
                      link_url: block.settings.link_url,
                      content: block.settings.content,
                      text_alignment: block.settings.text_alignment,
                      image: block.settings.image,
                      video_shopify: block.settings.video_shopify,
                      text_width: block.settings.text_width,
                      text_type_scale: block.settings.text_type_scale,
                      size: section.settings.card_size,
                      button_label: block.settings.button_label,
                      button_style: block.settings.button_style
                  -%}
                </li>
              {%- endif -%}
            {%- endfor -%}
          </ul>

          {%- if paginate.pages > 1 -%}
            {% render 'pagination', paginate: paginate, class: "mb-10", style: settings.pagination_style %}
          {%- endif -%}
        {%- endif -%}
      </div>
    </custom-pagination>
  {%- endpaginate -%}
{%- else -%}
  {%- comment -%}RECOMMENDATIONS-STYLE PRODUCT FILTERING - CUSTOMER RESTRICTIONS ACTIVE{%- endcomment -%}
  <div class="main-products-grid{% if settings.card_contain %} main-products-grid--contained{% endif %} flex" data-layout="{{ section.settings.product_display_mode }}">
    <div class="main-products-grid__results relative flex-auto" id="filter-results">
      
      {%- if total_items == 0 -%}
        <p>No products are available for your location.</p>
      {%- else -%}
        
        {%- comment -%}
        <!-- Debug info removed from production -->
        {%- endcomment -%}
        
        <div class="mb-4 text-center">
          <p>Showing {{ total_items }} products available for your location</p>
        </div>
        
        <ul class="grid mb-8 md:mb-12 {% if section.settings.filters_open_lg %}{{ grid_classes_filters_open }}{% else %}{{ grid_classes_filters_closed }}{% endif %} gap-x-theme {% if settings.card_contain %}gap-y-5 md:gap-y-8{% else %}gap-y-16{% endif %}" role="list">
          
          {%- comment -%}FETCH PRODUCTS BY HANDLE - USING ALL_PRODUCTS{%- endcomment -%}
          {%- for handle in paginated_handles -%}
            {%- assign handle = handle | strip -%}
            {%- assign handle_length = handle | size -%}
            {%- assign handle_json = handle | json -%}
            {%- assign handle_encoded = handle | url_encode -%}
            {%- assign product = all_products[handle] -%}
            {%- if product -%}
              <li class="js-pagination-result">
                {% render 'product-card', product: product, collection: collection, image_ratio: image_ratio, show_compare: settings.enable_compare %}
              </li>
              {%- assign found_product = true -%}
            {%- endif -%}
            
            {%- unless found_product -%}
              {%- comment -%}<!-- Product not found, silently skip -->{%- endcomment -%}
            {%- endunless -%}
          {%- endfor -%}
          
        </ul>

        {%- comment -%}CUSTOM PAGINATION FOR RESTRICTED PRODUCTS{%- endcomment -%}
        {%- if total_pages > 1 -%}
          <nav class="pagination" role="navigation" aria-label="Pagination">
            <ul class="pagination__list list-unstyled" role="list">
              
              {%- if current_page > 1 -%}
                <li>
                  <a href="?page={{ current_page | minus: 1 }}" class="pagination__item pagination__item--prev">
                    {% render 'icon', icon: 'chevron-left' %}
                    <span class="visually-hidden">{{ 'general.pagination.previous' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              
              {%- for i in (1..total_pages) -%}
                <li>
                  {%- if i == current_page -%}
                    <span class="pagination__item pagination__item--current" aria-current="page">{{ i }}</span>
                  {%- else -%}
                    <a href="?page={{ i }}" class="pagination__item">{{ i }}</a>
                  {%- endif -%}
                </li>
              {%- endfor -%}
              
              {%- if current_page < total_pages -%}
                {% comment %} <li>
                  <a href="?page={{ current_page | plus: 1 }}" class="pagination__item pagination__item--next">
                    <span class="visually-hidden">{{ 'general.pagination.next' | t }}</span>
                    {% render 'icon', icon: 'chevron-right' %}
                  </a>
                </li> {% endcomment %}
              {%- endif -%}
              
            </ul>
          </nav>
        {%- endif -%}

      {%- endif -%}
    </div>
  </div>
{%- endif -%}
</div>

{% schema %}
{
  "name": "Collection products",
  "class": "cc-collection-products section section--template mb-0",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 50,
      "step": 1,
      "label": "Products per page",
      "default": 16
    },
    {
      "type": "select",
      "id": "product_display_mode",
      "label": "Product display mode",
      "info": "Choose between grid or list view for product collections.",
      "options": [
        {
          "value": "grid",
          "label": "Grid View"
        },
        {
          "value": "list",
          "label": "List View"
        }
      ],
      "default": "grid"
    },
    {
      "type": "select",
      "id": "card_size_mobile",
      "label": "Product card size on mobile",
      "info": "Applies to cards in Grid view only.",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "small"
    },
    {
      "type": "select",
      "id": "card_size",
      "label": "Product card size on large screens",
      "info": "Applies to cards in Grid view only.",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "Toolbar"
    },
    {
      "type": "paragraph",
      "content": "The Compare toggle can be disabled in \"Theme Settings > Product Compare\"."
    },
    {
      "type": "checkbox",
      "id": "show_layout_toggle",
      "label": "Show grid/list layout buttons",
      "default": true
    },
    {
      "type": "header",
      "content": "Filtering"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "label": "Enable filtering",
      "info": "[Customize filters](/admin/menus)",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "filters_open_lg",
      "label": "Open filters by default on large screens",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stick_on_scroll",
      "label": "Stick on scroll on large screens",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_filter_counts",
      "label": "Show product counts",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "expand_filters",
      "label": "Expand all filters by default",
      "default": true
    },
    {
      "type": "range",
      "id": "max_filter_options",
      "label": "Visible options per filter",
      "info": "Remaining options will be automatically collapsed.",
      "min": 2,
      "max": 50,
      "step": 1,
      "default": 7
    },
    {
      "type": "header",
      "content": "Sorting"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "label": "Enable sorting",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_feat_sort_opt",
      "label": "Show \"Featured\" option",
      "info": "Add your manually sorted order to the sort options.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "sort_first",
      "label": "Show the sort options first on mobile",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "wide-promotion",
      "name": "Wide promotion",
      "settings": [
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "select",
          "id": "position",
          "label": "Position in results",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "view",
          "label": "Visibility",
          "info": "Promos will be full width on List view",
          "options": [
            {
              "value": "grid",
              "label": "Grid view"
            },
            {
              "value": "list",
              "label": "List view"
            },
            {
              "value": "grid_list",
              "label": "Grid and List view"
            }
          ],
          "default": "grid_list"
        },
        {
          "type": "checkbox",
          "id": "hide_on_filter",
          "label": "Hide when results are filtered",
          "default": true
        },
        {
          "type": "range",
          "id": "min_height",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Minimum height",
          "info": "Useful for controlling the height of the promo in List view and on mobile.",
          "default": 150
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video_shopify",
          "label": "Video upload",
          "info": "Hosted by Shopify. Video will autoplay and be muted. [Read more](https://cleancanvas.co.uk/support/enterprise/using-videos)"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "richtext",
          "id": "content",
          "default": "<p>Promotion text</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_type_scale",
          "label": "Text size",
          "info": "This is calculated from the Heading font size.",
          "options": [
            {
              "value": "1em",
              "label": "Body font size"
            },
            {
              "value": "--h6-font-size",
              "label": "Extra small"
            },
            {
              "value": "--h5-font-size",
              "label": "Small"
            },
            {
              "value": "--h4-font-size",
              "label": "Medium"
            },
            {
              "value": "--h3-font-size",
              "label": "Large"
            },
            {
              "value": "--h2-font-size",
              "label": "Extra large"
            }
          ],
          "default": "--h4-font-size"
        },
        {
          "type": "range",
          "id": "text_width",
          "min": 100,
          "max": 800,
          "step": 20,
          "unit": "px",
          "label": "Maximum text width",
          "default": 400
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "default": "justify-center items-center text-center",
          "options": [
            {
              "value": "justify-start items-start",
              "label": "Top left"
            },
            {
              "value": "justify-center items-start text-center",
              "label": "Top center"
            },
            {
              "value": "justify-end items-start text-right",
              "label": "Top right"
            },
            {
              "value": "justify-start items-center",
              "label": "Middle left"
            },
            {
              "value": "justify-center items-center text-center",
              "label": "Middle center"
            },
            {
              "value": "justify-end items-center text-right",
              "label": "Middle right"
            },
            {
              "value": "justify-start items-end",
              "label": "Bottom left"
            },
            {
              "value": "justify-center items-end text-center",
              "label": "Bottom center"
            },
            {
              "value": "justify-end items-end text-right",
              "label": "Bottom right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "info": "Links to the URL specified in the \"Link URL\" setting."
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "btn btn--primary",
              "label": "Primary button"
            },
            {
              "value": "btn btn--secondary",
              "label": "Secondary button"
            }
          ],
          "default": "btn btn--primary"
        },
        {
          "type": "header",
          "content": "Style"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "tint_color",
          "label": "Tint color",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "tint_opacity",
          "min": 0,
          "max": 80,
          "step": 5,
          "unit": "%",
          "label": "Tint opacity",
          "default": 20
        }
      ]
    },
    {
      "type": "image-promotion",
      "name": "Media promotion",
      "settings": [
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "position",
          "label": "Position in results",
          "info": "Inserts the promotion before the product card in this position. Only applies to the first page of results.",
          "min": 1,
          "max": 50,
          "step": 1,
          "default": 1
        },
        {
          "type": "select",
          "id": "view",
          "label": "Visibility",
          "info": "Promos will be full width on List view",
          "options": [
            {
              "value": "grid",
              "label": "Grid view"
            },
            {
              "value": "list",
              "label": "List view"
            },
            {
              "value": "grid_list",
              "label": "Grid and List view"
            }
          ],
          "default": "grid_list"
        },
        {
          "type": "checkbox",
          "id": "hide_on_filter",
          "label": "Hide when results are filtered",
          "default": true
        },
        {
          "type": "range",
          "id": "min_height",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Minimum height",
          "info": "Useful for controlling the height of the promo in List view and on mobile.",
          "default": 150
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video_shopify",
          "label": "Video upload",
          "info": "Hosted by Shopify. Video will autoplay and be muted. [Read more](https://cleancanvas.co.uk/support/enterprise/using-videos)"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "richtext",
          "id": "content",
          "default": "<p>Promotion text</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_type_scale",
          "label": "Text size",
          "info": "This is calculated from the Heading font size.",
          "options": [
            {
              "value": "1em",
              "label": "Body font size"
            },
            {
              "value": "--h6-font-size",
              "label": "Extra small"
            },
            {
              "value": "--h5-font-size",
              "label": "Small"
            },
            {
              "value": "--h4-font-size",
              "label": "Medium"
            },
            {
              "value": "--h3-font-size",
              "label": "Large"
            }
          ],
          "default": "1em"
        },
        {
          "type": "range",
          "id": "text_width",
          "min": 100,
          "max": 800,
          "step": 20,
          "unit": "px",
          "label": "Maximum text width",
          "default": 400
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "default": "justify-center items-center text-center",
          "options": [
            {
              "value": "justify-start items-start",
              "label": "Top left"
            },
            {
              "value": "justify-center items-start text-center",
              "label": "Top center"
            },
            {
              "value": "justify-end items-start text-right",
              "label": "Top right"
            },
            {
              "value": "justify-start items-center",
              "label": "Middle left"
            },
            {
              "value": "justify-center items-center text-center",
              "label": "Middle center"
            },
            {
              "value": "justify-end items-center text-right",
              "label": "Middle right"
            },
            {
              "value": "justify-start items-end",
              "label": "Bottom left"
            },
            {
              "value": "justify-center items-end text-center",
              "label": "Bottom center"
            },
            {
              "value": "justify-end items-end text-right",
              "label": "Bottom right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "info": "Links to the URL specified in the \"Link URL\" setting."
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "btn btn--primary",
              "label": "Primary button"
            },
            {
              "value": "btn btn--secondary",
              "label": "Secondary button"
            }
          ],
          "default": "btn btn--secondary"
        },
        {
          "type": "header",
          "content": "Style"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "tint_color",
          "label": "Tint color",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "tint_opacity",
          "min": 0,
          "max": 80,
          "step": 5,
          "unit": "%",
          "label": "Tint opacity",
          "default": 20
        }
      ]
    },
    {
      "type": "card-promotion",
      "name": "Card promotion",
      "settings": [
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "position",
          "label": "Position in results",
          "info": "Inserts the promotion before the product card in this position. Only applies to the first page of results.",
          "min": 1,
          "max": 50,
          "step": 1,
          "default": 1
        },
        {
          "type": "select",
          "id": "view",
          "label": "Visibility",
          "info": "Promos will be full width on List view",
          "options": [
            {
              "value": "grid",
              "label": "Grid view"
            },
            {
              "value": "list",
              "label": "List view"
            },
            {
              "value": "grid_list",
              "label": "Grid and List view"
            }
          ],
          "default": "grid_list"
        },
        {
          "type": "checkbox",
          "id": "hide_on_filter",
          "label": "Hide when results are filtered",
          "default": true
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "label": "Color scheme",
          "options": [
            {
              "value": "1",
              "label": "Scheme 1"
            },
            {
              "value": "2",
              "label": "Scheme 2"
            },
            {
              "value": "3",
              "label": "Scheme 3"
            }
          ],
          "default": "1"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "label": "Image aspect ratio",
          "options": [
            {
              "value": "natural",
              "label": "Natural"
            },
            {
              "value": "fill",
              "label": "Grow"
            },
            {
              "value": "1.77",
              "label": "Landscape"
            },
            {
              "value": "1",
              "label": "Square"
            },
            {
              "value": "0.75",
              "label": "Portrait"
            }
          ],
          "default": "1"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Promotion title"
        },
        {
          "type": "richtext",
          "id": "content",
          "default": "<p>Promotion text</p>",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Button",
          "info": "The button will link to the URL specified above."
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "btn btn--primary",
              "label": "Primary button"
            },
            {
              "value": "btn btn--secondary",
              "label": "Secondary button"
            }
          ],
          "default": "btn btn--secondary"
        },
        {
          "type": "checkbox",
          "id": "button_bottom_align",
          "label": "Bottom align button",
          "default": true
        }
      ]
    },
    {
      "type": "facet-image-promotion",
      "limit": 1,
      "name": "Filter promotion",
      "settings": [
        {
          "type": "paragraph",
          "content": "Shows on large screens only."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video_shopify",
          "label": "Video upload",
          "info": "Hosted by Shopify. Video will autoplay and be muted. [Read more](https://cleancanvas.co.uk/support/enterprise/using-videos)"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "richtext",
          "id": "content",
          "default": "<p>Promotion text</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_type_scale",
          "label": "Text size",
          "info": "This is calculated from the Heading font size.",
          "options": [
            {
              "value": "1em",
              "label": "Body font size"
            },
            {
              "value": "--h6-font-size",
              "label": "Extra small"
            },
            {
              "value": "--h5-font-size",
              "label": "Small"
            },
            {
              "value": "--h4-font-size",
              "label": "Medium"
            },
            {
              "value": "--h3-font-size",
              "label": "Large"
            }
          ],
          "default": "1em"
        },
        {
          "type": "range",
          "id": "min_height",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Minimum height",
          "default": 300
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "default": "justify-center items-center text-center",
          "options": [
            {
              "value": "justify-start items-start",
              "label": "Top left"
            },
            {
              "value": "justify-center items-start text-center",
              "label": "Top center"
            },
            {
              "value": "justify-end items-start text-right",
              "label": "Top right"
            },
            {
              "value": "justify-start items-center",
              "label": "Middle left"
            },
            {
              "value": "justify-center items-center text-center",
              "label": "Middle center"
            },
            {
              "value": "justify-end items-center text-right",
              "label": "Middle right"
            },
            {
              "value": "justify-start items-end",
              "label": "Bottom left"
            },
            {
              "value": "justify-center items-end text-center",
              "label": "Bottom center"
            },
            {
              "value": "justify-end items-end text-right",
              "label": "Bottom right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "info": "Links to the URL specified in the \"Link URL\" setting."
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "btn btn--primary",
              "label": "Primary button"
            },
            {
              "value": "btn btn--secondary",
              "label": "Secondary button"
            }
          ],
          "default": "btn btn--secondary"
        },
        {
          "type": "header",
          "content": "Style"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "tint_color",
          "label": "Tint color",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "tint_opacity",
          "min": 0,
          "max": 80,
          "step": 5,
          "unit": "%",
          "label": "Tint opacity",
          "default": 20
        }
      ]
    }
  ]
}
{% endschema %}
