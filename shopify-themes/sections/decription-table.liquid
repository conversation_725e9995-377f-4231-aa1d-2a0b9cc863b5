<section class="section product-section" style="margin:0; padding:40px 0;">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-x-theme gap-y-8">
        {% if product.description %}
          <!-- Description Column 1 -->
          <div class="lg:col-span-2">
            <div class="product-description">
              <h2 class="h4 section__heading text-start mb-heading">Description</h2>
              <div style="margin-top:30px;">{{ product.description }}</div>
            </div>
          </div>
        
        {% endif %}
  
        <!-- Specifications Column -->
        <div class="lg:col-span-1">
          {% comment %} {% assign custom_specs = "brand, range, capacity, area, type_of_cleaner, en_certifications, product_type, dilution, size, colour, paper_ply, paper_fold, sheets_per_case, animated_usage_guide, en_numbers, roll_per_case, roll_length, weight, pallet_qty, product_spec_sheet, roll_width, roll_diameter, sheet_width, sheet_length, video_link, sheets_per_roll, material, cases_per_pallet, fibre, machine_weight_kg, motor_wattage, cable_length, case_quantity, length_x_width_x_height, chsa_rating, fitment, mop_type, power_source, battery_voltage, recharge_mini, cleaning_width, run_time, solution_tank, recovery_tank, product_catalogue, machine_type, diemensions, sheets_per_sleeve, decibels, charge_time, airflow_per_second, cord_length, dose" | split: ", " %} {% endcomment %}
          {% assign custom_specs = "brand, range, product_type, airflow_per_second, animated_usage_guide, area, battery_voltage, brush_pad_size, brush_pad_pressure_kg, chsa_rating, cable_length, capacity, carpet_wand, case_quantity, cases_per_pallet, charge_time, chassis, cleaning_width, colour, cord_length, coverage_estimate_sqm, coverage_theoretical_sqm, decibels, diametre, diemensions, dilution, dose, en_certifications, en_numbers, engine, engine_power, fabric_mix, fabric_type, fibre, film, fitment, front_castors, gearbox, graphic_size, hose_length, leg_length, length_x_width_x_height, machine_type, machine_weight_kg, material, mop_type, motor_wattage, neck_type, overlaminate, pallet_qty, paper_fold, paper_ply, power_source, pressure_psi, recharge_mini, recovery_tank, roll_diameter, roll_length, roll_width, roll_per_case, run_time, sheet_length, sheet_width, sheets_per_case, sheets_per_roll, sheets_per_sleeve, shipping_diemensions, shipping_weight, size, sleeve_type, solution_tank, sound_level_dba, speed, tank_construction, type_of_cleaner, vacuum_motor, waist_size, warranty, water_flow_per_minute, water_lift_airflow, weight, wheels, ph, sustainability, certifications, en_number" | split: ", " %}
          {% assign my_fields_specs = "colour_name, size_description, size_exclusions, washing_instructions, fabric, weight_gsm, gender, age_group" | split: ", " %}
          {% comment %} THIS IS THE ONE WE HAVE CREATED FOR ROSS FROM SCRATCH {% endcomment %}
          {% assign spec_table_specs = "battery_amp, battery_type, brush_pad_pressure_kg, brush_pad_size_cm, cable_length_metre, category, charge_time, cleaning_width_cm, coverage_sqm, dimensions_l_x_w_x_h, dosage_dilution, hose_length_metre, sound_level_db, speed_rpm, sack_carry_weight_kg, waterflow" | split: ", " %}
  
          {% assign has_specifications = false %}
  
          {% for spec in custom_specs %}
            {% if product.metafields.custom[spec] %}
              {% assign has_specifications = true %}
              {% break %}
            {% endif %}
          {% endfor %}
  
          {% for spec in my_fields_specs %}
            {% if product.metafields.my_fields[spec] %}
              {% assign has_specifications = true %}
              {% break %}
            {% endif %}
          {% endfor %}

          {% for spec in spec_table_specs %}
            {% if product.metafields.spec_table[spec] %}
              {% assign has_specifications = true %}
              {% break %}
            {% endif %}
          {% endfor %}
  
          {% if has_specifications %}
            <div id="specificationsSection">
              <h2 class="h4 section__heading text-start mb-heading">Product Specifications</h2>
              <table class="table table--alt" style="width:100%; margin:30px 0">
                {% for spec in custom_specs %}
                  {% if product.metafields.custom[spec] %}
                    <tr>
                      <th>{{ spec | replace: "_", " " | capitalize }}</th>
                      <td>{{ product.metafields.custom[spec] }}</td>
                    </tr>
                  {% endif %}
                {% endfor %}
                {% for spec in my_fields_specs %}
                  {% if product.metafields.my_fields[spec] %}
                    <tr>
                      <th>{{ spec | replace: "_", " " | capitalize }}</th>
                      <td>{{ product.metafields.my_fields[spec] }}</td>
                    </tr>
                  {% endif %}
                {% endfor %}
              </table>
              {% if product.metafields.custom.disclaimer %}
              <p><strong>{{product.metafields.custom.disclaimer}}</strong></p>
              {% endif %}
            </div>
          {% endif %}

          {% if product.metafields.custom.coshhsds 
            or product.metafields.custom.usage_guide_url 
            or product.metafields.custom.risk_assessment_url 
            or product.metafields.custom.product_specification 
            or product.metafields.custom.product_catalogue 
            or product.metafields.custom.product_spec_sheet 
            or product.metafields.custom.technical_data 
            or product.metafields.custom.instruction_manual 
            or product.metafields.download.safety_data_sheet
            or product.metafields.download.product_usage_guide
            or product.metafields.download.risk_assessment
            or product.metafields.download.instruction_manual
            or product.metafields.download.parts_list
            or product.metafields.download.spares_list
            or product.metafields.download.sales_literature
            or product.metafields.download.installation_guide %}

            <div id="downloadsSection">
                <h2 class="h4 section__heading text-start mb-heading">Downloads</h2>
              <div class="guides" style="margin-top:40px;">

              {% if product.metafields.custom.video_link %}
                <div class="product-coshh-link downloadLink">
                  <a href="{{ product.metafields.custom.video_link.value }}" class="downloadLink" target="_blank" >
                    <img src="{{ 'video.png' | asset_url }}" alt="YouTube Video" width="80" height="80">
                    <span>YouTube Video</span> 
                  </a>
                </div>
              {% endif %}

              {% if product.metafields.custom.animated_usage_guide %}
                <div class="product-coshh-link downloadLink">
                  <a href="{{ product.metafields.custom.animated_usage_guide.value }}" class="downloadLink" target="_blank" >
                    <img src="{{ 'video.png' | asset_url }}" alt="YouTube Video" width="80" height="80">
                    <span>Animated Usage Guide</span> 
                  </a>
                </div>
              {% endif %}

              {% assign downloads = 
                "Safety Data Sheet,Product Usage Guide,Risk Assessment,Instruction Manual,Parts List,Spares List,Sales Literature,Installation Guide" | split: "," %}
              
              {% for download in downloads %}
                {% assign key = download | downcase | replace: " ", "_" %}
                {% assign file = product.metafields.download[key] %}
              
                {% if file %}
                  {% assign icon = 'PUG.png' %}
                  
                  {% if key == "risk_assessment" %}
                    {% assign icon = 'risk-assessment.png' %}
                  {% elsif key == "safety_data_sheet" %}
                    {% assign icon = 'COSHH.png' %}
                  {% endif %}
                  
                  <div class="product-{{ key }}-link downloadLink">
                    <a href="{{ file | file_url }}" class="downloadLink" target="_blank" >
                      <img src="{{ icon | asset_url }}" alt="{{ download }}" width="80" height="80">
                      <span>{{ download }}</span> 
                    </a>
                  </div>
                {% endif %}
              {% endfor %}

              {% if product.metafields.custom.coshhsds %}
                <div class="product-coshh-link downloadLink">
                  <a href="{{ product.metafields.custom.coshhsds.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'COSHH.png' | asset_url }}" alt="COSHH Information" width="80" height="80">
                    <span>Saftey Data Sheet</span> 
                  </a>
                  
                </div>
              {% endif %}
    
              {% if product.metafields.custom.usage_guide_url %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.usage_guide_url.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Usage Guide" width="80" height="80"> 
                    <span>Usage Guide</span>
                  </a>
                  
                </div>
              {% endif %}
              {% if product.metafields.custom.product_catalogue %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.product_catalogue.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Product Catalogue" width="80" height="80"> 
                    <span>Product Catalogue</span>
                  </a>
                  
                </div>
              {% endif %}
              {% if product.metafields.custom.technical_data %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.technical_data.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Technical Data" width="80" height="80"> 
                    <span>Technical Data</span>
                  </a>
                  
                </div>
              {% endif %}
              {% if product.metafields.custom.product_spec_sheet %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.product_spec_sheet.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Product Sepc Sheet" width="80" height="80"> 
                    <span>Product Spec Sheet</span>
                  </a>
                  
                </div>
              {% endif %}
              {% if product.metafields.custom.product_specification %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.product_specification.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Product Specification" width="80" height="80"> 
                    <span>Product Specification</span>
                  </a>
                  
                </div>
              {% endif %}
              {% if product.metafields.custom.instruction_manual %}
                <div class="product-pug-link downloadLink">
                  <a href="{{ product.metafields.custom.instruction_manual.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'PUG.png' | asset_url }}" alt="Instruction Manual" width="80" height="80"> 
                    <span>Instruction Manual</span>
                  </a>
                  
                </div>
              {% endif %}
    
              {% if product.metafields.custom.risk_assessment_url %}
                <div class="product-risk-assesment-link downloadLink">
                  <a href="{{ product.metafields.custom.risk_assessment_url.value }}" class="downloadLink" target="_blank">
                    <img src="{{ 'risk-assessment.png' | asset_url }}" alt="Risk Assesment" width="80" height="80">
                    <span>Risk Assesment</span> 
                  </a>
                  
                </div>
              {% endif %}
              </div>
            </div>
            {% endif %}
  
          
        </div>
      </div>
    </div>
  </section>

{% schema %}
{
  "name": "Description and table",
  "presets": [
    {
      "name": "Description and table"
    }
  ]
}
{% endschema %}
